#!/usr/bin/env python3
"""
测试并行查询执行和结果融合功能
"""

import asyncio
import time
import json
from src.agents.multi_agent_coordinator import MultiAgentCoordinator


async def test_parallel_execution():
    """测试并行执行和结果融合"""
    
    print("🧪 开始测试并行查询执行和结果融合功能...")
    
    # 创建协调器
    coordinator = MultiAgentCoordinator()
    
    # 注册事件回调以监控执行过程
    events = []
    
    async def event_callback(event_data):
        events.append(event_data)
        event_type = event_data.get("event_type", "unknown")
        message = event_data.get("message", "")
        timestamp = event_data.get("timestamp", time.time())
        
        print(f"[{time.strftime('%H:%M:%S', time.localtime(timestamp))}] {event_type}: {message}")
        
        # 如果有详细数据，打印部分信息
        if "data" in event_data:
            data = event_data["data"]
            if event_type == "plan_created":
                print(f"    查询类型: {data.get('query_type', 'N/A')}")
                print(f"    子查询数量: {len(data.get('sub_queries', []))}")
                print(f"    融合策略: {data.get('fusion_strategy', 'N/A')}")
            elif event_type == "parallel_execution_complete":
                print(f"    成功: {data.get('successful_count', 0)}")
                print(f"    失败: {data.get('failed_count', 0)}")
    
    coordinator.register_event_callback(event_callback)
    
    try:
        # 初始化协调器
        await coordinator.initialize()
        
        # 测试查询: 寻找包含特定组件且质量小于0.5kg的装配体
        test_query = (
            "Find assemblies that contain Miteebite T Nut and Miteebite Socket Head Cap Screw Ai, "
            "with mass less than 0.5 kg, volume greater than 30 cm³, and part count equal to 5."
        )
        
        print(f"\n🔍 执行测试查询:")
        print(f"   查询内容: {test_query}")
        print(f"   预期: 生成包含StructuralTopologyAgent和AttributeFilteringAgent的并行查询计划")
        print()
        
        start_time = time.time()
        
        # 执行查询
        result = await coordinator.execute_multi_modal_query(
            query_text=test_query,
            top_k=10
        )
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 查询执行完成:")
        print(f"   执行时间: {execution_time:.2f} 秒")
        print(f"   查询状态: {result.status}")
        print(f"   结果数量: {result.total_results}")
        
        if result.status == 'success':
            print(f"   ✅ 查询成功完成")
            if result.results:
                print(f"   前3个结果:")
                for i, item in enumerate(result.results[:3], 1):
                    print(f"      {i}. {item.name} (UUID: {item.uuid})")
                    print(f"         类型: {item.search_type}")
                    if item.similarity_score:
                        print(f"         相似度: {item.similarity_score:.3f}")
        else:
            print(f"   ❌ 查询失败: {result.error_message}")
        
        # 分析事件时序
        print(f"\n📈 事件分析:")
        plan_events = [e for e in events if e["event_type"] == "plan_created"]
        parallel_start_events = [e for e in events if e["event_type"] == "parallel_execution_start"]
        step_start_events = [e for e in events if e["event_type"] == "step_start"]
        step_complete_events = [e for e in events if e["event_type"] == "step_complete"]
        parallel_complete_events = [e for e in events if e["event_type"] == "parallel_execution_complete"]
        fusion_events = [e for e in events if e["event_type"] == "fusion_start"]
        
        print(f"   查询规划事件: {len(plan_events)}")
        print(f"   并行执行开始事件: {len(parallel_start_events)}")
        print(f"   子查询开始事件: {len(step_start_events)}")
        print(f"   子查询完成事件: {len(step_complete_events)}")
        print(f"   并行执行完成事件: {len(parallel_complete_events)}")
        print(f"   结果融合事件: {len(fusion_events)}")
        
        # 验证并行执行
        if step_start_events and step_complete_events:
            start_times = [e["timestamp"] for e in step_start_events]
            complete_times = [e["timestamp"] for e in step_complete_events]
            
            if len(start_times) > 1:
                max_start_gap = max(start_times) - min(start_times)
                print(f"   子查询开始时间差: {max_start_gap:.3f} 秒 (应该很小表示并行执行)")
                
                if max_start_gap < 0.1:  # 100ms内都开始执行
                    print(f"   ✅ 确认子查询是并行执行的")
                else:
                    print(f"   ⚠️ 子查询可能不是完全并行执行的")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return None
        
    finally:
        # 断开连接
        await coordinator.disconnect()
        coordinator.unregister_event_callback(event_callback)


async def test_different_fusion_strategies():
    """测试不同的融合策略"""
    print("\n🧪 测试不同的融合策略...")
    
    # 这里可以添加更多测试用例来验证不同融合策略的效果
    # 暂时跳过，因为需要实际的数据
    print("   (需要实际数据支持，暂时跳过)")


async def main():
    """主测试函数"""
    print("🚀 开始并行执行和结果融合测试套件")
    print("=" * 60)
    
    # 测试1: 基本并行执行
    result1 = await test_parallel_execution()
    
    # 测试2: 不同融合策略 (暂时跳过)
    await test_different_fusion_strategies()
    
    print("\n" + "=" * 60)
    print("✅ 测试套件完成")


if __name__ == "__main__":
    asyncio.run(main())
