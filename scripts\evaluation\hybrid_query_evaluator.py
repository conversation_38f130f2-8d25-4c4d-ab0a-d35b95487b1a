#!/usr/bin/env python3
"""
混合查询评估器
用于评估多智能体系统在混合查询基准测试上的准确率
"""

import os
import sys
import json
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from src.agents.multi_agent_coordinator import MultiAgentCoordinator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class EvaluationResult:
    """单个查询的评估结果"""
    query_id: str
    query_type: str
    natural_language_prompt: str
    ground_truth_uuids: List[str]
    predicted_uuids: List[str]
    is_correct: bool
    execution_time: float
    error_message: Optional[str] = None


@dataclass
class EvaluationSummary:
    """评估总结"""
    total_queries: int
    correct_predictions: int
    accuracy: float
    avg_execution_time: float
    query_type_accuracy: Dict[str, float]
    failed_queries: int
    error_rate: float


class HybridQueryEvaluator:
    """混合查询评估器"""
    
    def __init__(self, benchmark_dir: str = "data/benchmark"):
        """
        初始化评估器
        
        Args:
            benchmark_dir: 基准测试数据目录
        """
        self.benchmark_dir = Path(benchmark_dir)
        self.coordinator = MultiAgentCoordinator()
        self.results: List[EvaluationResult] = []
        
    async def initialize(self):
        """初始化多智能体协调器"""
        logger.info("正在初始化多智能体协调器...")
        await self.coordinator.initialize()
        logger.info("多智能体协调器初始化完成")
    
    async def evaluate_all_hybrid_queries(self, 
                                        max_assemblies: Optional[int] = None,
                                        top_k: int = 10) -> EvaluationSummary:
        """
        评估所有混合查询
        
        Args:
            max_assemblies: 最大评估装配体数量（None表示评估所有）
            top_k: 每个查询返回的结果数量
            
        Returns:
            评估总结
        """
        logger.info("开始评估混合查询...")
        
        # 获取所有装配体目录
        assembly_dirs = [d for d in self.benchmark_dir.iterdir() 
                        if d.is_dir() and d.name != "generation_statistics.json"]
        
        if max_assemblies:
            assembly_dirs = assembly_dirs[:max_assemblies]
            
        logger.info(f"找到 {len(assembly_dirs)} 个装配体，开始评估...")
        
        total_queries = 0
        processed_queries = 0
        
        for assembly_dir in assembly_dirs:
            hybrid_query_file = assembly_dir / "Hybrid_query.json"
            
            if not hybrid_query_file.exists():
                logger.warning(f"装配体 {assembly_dir.name} 缺少混合查询文件")
                continue
                
            # 加载混合查询
            try:
                with open(hybrid_query_file, 'r', encoding='utf-8') as f:
                    hybrid_queries = json.load(f)
                    
                total_queries += len(hybrid_queries)
                
                # 评估每个混合查询
                for query in hybrid_queries:
                    try:
                        result = await self._evaluate_single_query(query, top_k)
                        self.results.append(result)
                        processed_queries += 1
                        
                        logger.info(f"查询 {query['query_id']} 评估完成: "
                                  f"{'正确' if result.is_correct else '错误'} "
                                  f"(耗时: {result.execution_time:.2f}s)")
                        
                        # 添加延迟避免过于频繁的请求
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        logger.error(f"评估查询 {query.get('query_id', 'unknown')} 失败: {e}")
                        # 记录失败的查询
                        error_result = EvaluationResult(
                            query_id=query.get('query_id', 'unknown'),
                            query_type=query.get('query_type', 'unknown'),
                            natural_language_prompt=query.get('natural_language_prompt', ''),
                            ground_truth_uuids=[],
                            predicted_uuids=[],
                            is_correct=False,
                            execution_time=0.0,
                            error_message=str(e)
                        )
                        self.results.append(error_result)
                        
            except Exception as e:
                logger.error(f"加载装配体 {assembly_dir.name} 的混合查询失败: {e}")
                continue
        
        logger.info(f"评估完成: 总查询数 {total_queries}, 处理查询数 {processed_queries}")
        
        # 生成评估总结
        summary = self._generate_summary()
        return summary
    
    async def _evaluate_single_query(self, query: Dict[str, Any], top_k: int) -> EvaluationResult:
        """
        评估单个查询
        
        Args:
            query: 查询数据
            top_k: 返回结果数量
            
        Returns:
            评估结果
        """
        query_id = query['query_id']
        query_type = query['query_type']
        natural_language_prompt = query['natural_language_prompt']
        
        # 提取ground truth UUIDs
        ground_truth_uuids = []
        for gt_result in query.get('ground_truth_results', []):
            if 'uuid' in gt_result:
                ground_truth_uuids.append(gt_result['uuid'])
        
        start_time = time.time()
        
        try:
            # 执行查询
            result = await self.coordinator.execute_multi_modal_query(
                query_text=natural_language_prompt,
                shape_vector=None,  # 混合查询通常不包含形状向量
                top_k=top_k
            )
            
            execution_time = time.time() - start_time
            
            # 提取预测的UUIDs
            predicted_uuids = []
            if result.status == 'success' and result.results:
                predicted_uuids = [item.uuid for item in result.results]
            
            # 判断是否正确：预测结果中是否包含任何ground truth UUID
            is_correct = any(uuid in predicted_uuids for uuid in ground_truth_uuids)
            
            return EvaluationResult(
                query_id=query_id,
                query_type=query_type,
                natural_language_prompt=natural_language_prompt,
                ground_truth_uuids=ground_truth_uuids,
                predicted_uuids=predicted_uuids,
                is_correct=is_correct,
                execution_time=execution_time,
                error_message=result.error_message if result.status != 'success' else None
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return EvaluationResult(
                query_id=query_id,
                query_type=query_type,
                natural_language_prompt=natural_language_prompt,
                ground_truth_uuids=ground_truth_uuids,
                predicted_uuids=[],
                is_correct=False,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def _generate_summary(self) -> EvaluationSummary:
        """生成评估总结"""
        total_queries = len(self.results)
        correct_predictions = sum(1 for r in self.results if r.is_correct)
        failed_queries = sum(1 for r in self.results if r.error_message is not None)
        
        accuracy = correct_predictions / total_queries if total_queries > 0 else 0.0
        error_rate = failed_queries / total_queries if total_queries > 0 else 0.0
        
        # 计算平均执行时间（排除失败的查询）
        successful_times = [r.execution_time for r in self.results if r.error_message is None]
        avg_execution_time = sum(successful_times) / len(successful_times) if successful_times else 0.0
        
        # 按查询类型计算准确率
        query_type_accuracy = {}
        query_types = set(r.query_type for r in self.results)
        
        for query_type in query_types:
            type_results = [r for r in self.results if r.query_type == query_type]
            type_correct = sum(1 for r in type_results if r.is_correct)
            type_accuracy = type_correct / len(type_results) if type_results else 0.0
            query_type_accuracy[query_type] = type_accuracy
        
        return EvaluationSummary(
            total_queries=total_queries,
            correct_predictions=correct_predictions,
            accuracy=accuracy,
            avg_execution_time=avg_execution_time,
            query_type_accuracy=query_type_accuracy,
            failed_queries=failed_queries,
            error_rate=error_rate
        )
    
    def save_detailed_results(self, output_file: str):
        """保存详细的评估结果"""
        results_data = []
        
        for result in self.results:
            results_data.append({
                'query_id': result.query_id,
                'query_type': result.query_type,
                'natural_language_prompt': result.natural_language_prompt,
                'ground_truth_uuids': result.ground_truth_uuids,
                'predicted_uuids': result.predicted_uuids,
                'is_correct': result.is_correct,
                'execution_time': result.execution_time,
                'error_message': result.error_message
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"详细评估结果已保存到: {output_file}")
    
    def print_summary(self, summary: EvaluationSummary):
        """打印评估总结"""
        print("\n" + "="*60)
        print("混合查询评估结果总结")
        print("="*60)
        print(f"总查询数: {summary.total_queries}")
        print(f"正确预测数: {summary.correct_predictions}")
        print(f"整体准确率: {summary.accuracy:.2%}")
        print(f"平均执行时间: {summary.avg_execution_time:.2f}秒")
        print(f"失败查询数: {summary.failed_queries}")
        print(f"错误率: {summary.error_rate:.2%}")
        
        print("\n按查询类型的准确率:")
        print("-" * 40)
        for query_type, accuracy in summary.query_type_accuracy.items():
            print(f"{query_type}: {accuracy:.2%}")
        
        print("="*60)


async def main():
    """主函数"""
    evaluator = HybridQueryEvaluator()
    
    try:
        # 初始化评估器
        await evaluator.initialize()
        
        # 执行评估（可以设置max_assemblies限制评估数量）
        summary = await evaluator.evaluate_all_hybrid_queries(
            max_assemblies=10,  # 先评估10个装配体进行测试
            top_k=10
        )
        
        # 打印总结
        evaluator.print_summary(summary)
        
        # 保存详细结果
        output_dir = Path("results/evaluation")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        detailed_results_file = output_dir / f"hybrid_query_evaluation_{timestamp}.json"
        evaluator.save_detailed_results(str(detailed_results_file))
        
        # 保存总结
        summary_file = output_dir / f"evaluation_summary_{timestamp}.json"
        summary_data = {
            'total_queries': summary.total_queries,
            'correct_predictions': summary.correct_predictions,
            'accuracy': summary.accuracy,
            'avg_execution_time': summary.avg_execution_time,
            'query_type_accuracy': summary.query_type_accuracy,
            'failed_queries': summary.failed_queries,
            'error_rate': summary.error_rate
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"评估总结已保存到: {summary_file}")
        
    except Exception as e:
        logger.error(f"评估过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
