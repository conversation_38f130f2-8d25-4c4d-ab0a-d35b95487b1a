#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Neo4j装配体图转换为PyTorch Geometric格式（对比学习优化版）

本脚本专门为对比学习优化图结构，目标是学习装配体的向量表示。

优化特性：
1. 移除Feature节点，简化图结构
2. 使用层次结构图，适合对比学习
3. 支持多种初始化方法（zero, bottom_up, random）
4. 针对对比学习进行优化

使用方法：
python scripts/neo4j_to_pyg_optimized.py [--init-method zero|bottom_up|random]
"""

import os
import sys
import argparse
import logging
import pickle
from typing import Dict, List, Optional, Tuple, Any
import numpy as np

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from torch_geometric.data import Data
from src.knowledge_graph import CADKnowledgeGraph

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedNeo4jToPyGConverter:
    """优化的Neo4j装配体图转换器，专门用于对比学习"""
    
    def __init__(self,
                 part_features_path: str = "dataset/fused_part_features.pkl",
                 feature_dim: int = 64,
                 learnable_init_std: float = 0.01,
                 init_method: str = "zero",
                 aggregation_mode: str = "mean"):
        """
        初始化转换器

        Args:
            part_features_path: 零件特征文件路径
            feature_dim: 特征维度
            learnable_init_std: 随机初始化的标准差
            init_method: 初始化方法 ('zero', 'bottom_up', 'random')
            aggregation_mode: 自底向上聚合模式 ('mean', 'max')
        """
        self.kg = CADKnowledgeGraph.from_config()
        self.part_features_path = part_features_path
        self.feature_dim = feature_dim
        self.learnable_init_std = learnable_init_std
        self.init_method = init_method
        self.aggregation_mode = aggregation_mode
        self.part_features = None

        # 加载零件特征
        self._load_part_features()
    
    def _load_part_features(self):
        """加载零件特征"""
        try:
            if os.path.exists(self.part_features_path):
                with open(self.part_features_path, 'rb') as f:
                    features_data = pickle.load(f)
                
                if isinstance(features_data, dict):
                    self.part_features = features_data
                    if self.part_features:
                        first_feature = next(iter(self.part_features.values()))
                        if hasattr(first_feature, 'shape'):
                            self.feature_dim = first_feature.shape[0]
                        else:
                            self.feature_dim = len(first_feature)
                    
                    logger.info(f"成功加载 {len(self.part_features)} 个零件特征，维度: {self.feature_dim}")
                else:
                    logger.warning("未识别的特征文件格式")
                    self.part_features = {}
            else:
                logger.warning(f"特征文件不存在: {self.part_features_path}")
                self.part_features = {}
        except Exception as e:
            logger.error(f"加载零件特征失败: {e}")
            self.part_features = {}
    
    def get_assembly_graph_data(self, assembly_id: str) -> Optional[Dict[str, Any]]:
        """获取装配体图数据"""
        return self._get_hierarchical_graph_data(assembly_id)
    
    def _get_hierarchical_graph_data(self, assembly_id: str) -> Optional[Dict[str, Any]]:
        """获取层次结构图数据"""
        query = f"""
        MATCH path = (a:Assembly {{uuid: '{assembly_id}'}})-[*0..]->(n)
        WHERE n:Assembly OR n:SubAssembly OR n:Part
        WITH collect(DISTINCT n) as all_nodes
        UNWIND all_nodes as node
        OPTIONAL MATCH (node)-[r]->(target)
        WHERE target IN all_nodes AND type(r) = 'hasComponent'
        RETURN 
            node.uuid as node_id,
            labels(node)[0] as node_type,
            node as node_props,
            type(r) as rel_type,
            target.uuid as target_id
        """
        
        try:
            result = self.kg.run_cypher(query)
            if isinstance(result, list):
                return self._process_graph_result(result, assembly_id)
            else:
                logger.error(f"查询装配体 {assembly_id} 失败: {result}")
                return None
        except Exception as e:
            logger.error(f"获取装配体 {assembly_id} 图数据失败: {e}")
            return None
    

    
    def _process_graph_result(self, result: List[Dict], assembly_id: str) -> Dict[str, Any]:
        """处理Neo4j查询结果"""
        nodes = {}
        edges = []
        
        for record in result:
            node_id = record['node_id']
            node_type = record['node_type']
            node_props = record['node_props']
            
            if node_id not in nodes:
                nodes[node_id] = {
                    'type': node_type,
                    'properties': dict(node_props) if node_props else {}
                }
            
            if record['target_id'] and record['rel_type']:
                edges.append({
                    'source': node_id,
                    'target': record['target_id'],
                    'type': record['rel_type']
                })
        
        return {
            'assembly_id': assembly_id,
            'nodes': nodes,
            'edges': edges
        }
    
    def _compute_bottom_up_features(self, nodes: Dict, edges: List, node_ids: List) -> Dict[str, np.ndarray]:
        """计算自底向上的节点特征"""
        # 构建父子关系图
        children = {node_id: [] for node_id in node_ids}
        parents = {node_id: [] for node_id in node_ids}

        for edge in edges:
            parent_id = edge['source']
            child_id = edge['target']
            if parent_id in children and child_id in children:
                children[parent_id].append(child_id)
                parents[child_id].append(parent_id)

        # 初始化特征字典
        node_features = {}

        # 首先设置Part节点的特征
        for node_id in node_ids:
            node_type = nodes[node_id]['type']
            if node_type == 'Part':
                if node_id in self.part_features:
                    node_features[node_id] = self.part_features[node_id].copy()
                else:
                    node_features[node_id] = np.zeros(self.feature_dim)
                    logger.warning(f"零件 {node_id} 没有特征，使用零向量")

        # 拓扑排序：从叶子节点开始向上计算
        def compute_features_recursive(node_id):
            if node_id in node_features:
                return node_features[node_id]

            node_type = nodes[node_id]['type']
            child_ids = children[node_id]

            if not child_ids:
                # 叶子节点但不是Part（不应该发生，但防御性编程）
                if node_type == 'Part':
                    feature = self.part_features.get(node_id, np.zeros(self.feature_dim))
                else:
                    feature = np.zeros(self.feature_dim)
            else:
                # 计算所有子节点的特征
                child_features = []
                for child_id in child_ids:
                    child_feature = compute_features_recursive(child_id)
                    child_features.append(child_feature)

                # 根据聚合模式计算特征
                if child_features:
                    child_features_array = np.array(child_features)
                    if self.aggregation_mode == "mean":
                        feature = np.mean(child_features_array, axis=0)
                    elif self.aggregation_mode == "max":
                        feature = np.max(child_features_array, axis=0)
                    else:
                        # 默认使用平均池化
                        feature = np.mean(child_features_array, axis=0)
                else:
                    feature = np.zeros(self.feature_dim)

            node_features[node_id] = feature
            return feature

        # 为所有节点计算特征
        for node_id in node_ids:
            if node_id not in node_features:
                compute_features_recursive(node_id)

        return node_features

    def convert_to_pyg_data(self, graph_data: Dict[str, Any]) -> Data:
        """转换为PyTorch Geometric Data对象"""
        nodes = graph_data['nodes']
        edges = graph_data['edges']

        node_ids = list(nodes.keys())
        node_to_idx = {node_id: idx for idx, node_id in enumerate(node_ids)}

        # 根据初始化方法构建节点特征
        if self.init_method == "bottom_up":
            # 使用自底向上聚合方法
            node_features_dict = self._compute_bottom_up_features(nodes, edges, node_ids)
            node_features = [node_features_dict[node_id] for node_id in node_ids]
        else:
            # 使用传统方法
            node_features = []
            for node_id in node_ids:
                node_info = nodes[node_id]
                node_type = node_info['type']

                if node_type == 'Part':
                    # Part节点使用预训练特征
                    if node_id in self.part_features:
                        feature = self.part_features[node_id]
                    else:
                        feature = np.zeros(self.feature_dim)
                        logger.warning(f"零件 {node_id} 没有特征，使用零向量")
                else:
                    # Assembly和SubAssembly的初始化
                    if self.init_method == "zero":
                        feature = np.zeros(self.feature_dim)
                    elif self.init_method == "random":
                        feature = np.random.normal(0, self.learnable_init_std, self.feature_dim).astype(np.float32)
                    else:
                        feature = np.zeros(self.feature_dim)

                node_features.append(feature)

        # 构建节点类型
        node_types = []
        for node_id in node_ids:
            node_type = nodes[node_id]['type']
            type_mapping = {'Assembly': 0, 'SubAssembly': 1, 'Part': 2}
            node_types.append(type_mapping.get(node_type, -1))
        
        # 构建边
        edge_index = []
        edge_types = []
        
        for edge in edges:
            source_idx = node_to_idx.get(edge['source'])
            target_idx = node_to_idx.get(edge['target'])
            
            if source_idx is not None and target_idx is not None:
                edge_index.append([source_idx, target_idx])

                # 边类型编码 - 统一使用hasComponent关系
                edge_type_mapping = {'hasComponent': 0}
                edge_types.append(edge_type_mapping.get(edge['type'], -1))
        
        # 转换为张量
        x = torch.tensor(node_features, dtype=torch.float)
        node_type = torch.tensor(node_types, dtype=torch.long)
        
        if edge_index:
            edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
            edge_type = torch.tensor(edge_types, dtype=torch.long)
        else:
            edge_index = torch.empty((2, 0), dtype=torch.long)
            edge_type = torch.empty((0,), dtype=torch.long)
        
        # 找到Assembly节点的索引（用于对比学习的目标）
        assembly_node_idx = None
        for i, node_id in enumerate(node_ids):
            if nodes[node_id]['type'] == 'Assembly':
                assembly_node_idx = i
                break
        
        data = Data(
            x=x,
            edge_index=edge_index,
            edge_type=edge_type,
            node_type=node_type,
            assembly_id=graph_data['assembly_id'],
            node_ids=node_ids,
            assembly_node_idx=assembly_node_idx
        )
        
        return data
    
    def process_assembly(self, assembly_id: str, output_base_dir: str) -> bool:
        """处理单个装配体"""
        logger.info(f"开始处理装配体: {assembly_id}")

        graph_data = self.get_assembly_graph_data(assembly_id)
        if not graph_data:
            logger.error(f"获取装配体 {assembly_id} 图数据失败")
            return False

        try:
            pyg_data = self.convert_to_pyg_data(graph_data)
        except Exception as e:
            logger.error(f"转换装配体 {assembly_id} 为PyG格式失败: {e}")
            return False

        # 保存图
        output_dir = os.path.join(output_base_dir, assembly_id)
        os.makedirs(output_dir, exist_ok=True)

        output_path = os.path.join(output_dir, "assembly.pt")

        try:
            torch.save(pyg_data, output_path)
            logger.info(f"装配体图已保存到: {output_path}")
            logger.info(f"  节点数: {pyg_data.x.shape[0]}")
            logger.info(f"  边数: {pyg_data.edge_index.shape[1]}")
            logger.info(f"  Assembly节点索引: {pyg_data.assembly_node_idx}")
            return True
        except Exception as e:
            logger.error(f"保存装配体图失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.kg:
            self.kg.close()


def extract_assembly_representations(base_dir: str = "datasets/fusion360_assembly") -> Dict[str, np.ndarray]:
    """
    从保存的装配体pt文件中提取所有装配体节点的ID和向量表示

    Args:
        base_dir: 装配体文件基础目录

    Returns:
        Dict[str, np.ndarray]: {assembly_id: assembly_vector}
    """
    import os
    import torch

    assembly_representations = {}

    if not os.path.exists(base_dir):
        logger.error(f"基础目录不存在: {base_dir}")
        return assembly_representations

    # 遍历所有装配体目录
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)

        if os.path.isdir(item_path):
            # 构建图文件路径
            graph_file = os.path.join(item_path, "assembly.pt")

            if os.path.exists(graph_file):
                try:
                    # 加载图数据
                    data = torch.load(graph_file, map_location='cpu')

                    # 获取装配体节点的向量表示
                    if hasattr(data, 'assembly_node_idx') and data.assembly_node_idx is not None:
                        assembly_vector = data.x[data.assembly_node_idx].numpy()
                        assembly_id = data.assembly_id
                        assembly_representations[assembly_id] = assembly_vector

                        logger.debug(f"提取装配体 {assembly_id} 的向量表示，维度: {assembly_vector.shape}")
                    else:
                        logger.warning(f"装配体 {item} 没有assembly_node_idx")

                except Exception as e:
                    logger.error(f"加载装配体图文件失败 {graph_file}: {e}")
            else:
                logger.debug(f"图文件不存在: {graph_file}")

    logger.info(f"成功提取 {len(assembly_representations)} 个装配体的向量表示")
    return assembly_representations


def save_assembly_representations(representations: Dict[str, np.ndarray],
                                output_path: str = "dataset/assembly_representations.pkl"):
    """
    保存装配体向量表示到文件

    Args:
        representations: 装配体向量表示字典
        output_path: 输出文件路径
    """
    import pickle
    import os

    # 创建输出目录
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    try:
        with open(output_path, 'wb') as f:
            pickle.dump(representations, f)

        logger.info(f"装配体向量表示已保存到: {output_path}")
        logger.info(f"包含 {len(representations)} 个装配体")

        # 打印统计信息
        if representations:
            first_vector = next(iter(representations.values()))
            logger.info(f"向量维度: {first_vector.shape}")

    except Exception as e:
        logger.error(f"保存装配体向量表示失败: {e}")
        raise


def load_assembly_representations(input_path: str = "dataset/assembly_representations.pkl") -> Dict[str, np.ndarray]:
    """
    从文件加载装配体向量表示

    Args:
        input_path: 输入文件路径

    Returns:
        Dict[str, np.ndarray]: 装配体向量表示字典
    """
    import pickle

    try:
        with open(input_path, 'rb') as f:
            representations = pickle.load(f)

        logger.info(f"从 {input_path} 加载了 {len(representations)} 个装配体向量表示")

        if representations:
            first_vector = next(iter(representations.values()))
            logger.info(f"向量维度: {first_vector.shape}")

        return representations

    except Exception as e:
        logger.error(f"加载装配体向量表示失败: {e}")
        return {}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Neo4j装配体图转换器（对比学习优化版）')
    parser.add_argument('--part-features-path', default='dataset/fused_part_features.pkl',
                       help='零件特征文件路径')
    parser.add_argument('--output-base-dir', default='datasets/fusion360_assembly',
                       help='输出基础目录')
    parser.add_argument('--assembly-ids', nargs='+',
                       help='指定要处理的装配体ID列表')
    parser.add_argument('--max-assemblies', type=int,
                       help='限制处理的最大装配体数量')
    parser.add_argument('--feature-dim', type=int, default=64,
                       help='特征维度')
    parser.add_argument('--random-init-std', type=float, default=0.01,
                       help='随机初始化的标准差')
    parser.add_argument('--init-method', choices=['zero', 'bottom_up', 'random'],
                       default='bottom_up', help='Assembly和SubAssembly节点的初始化方法')
    parser.add_argument('--aggregation-mode', choices=['mean', 'max'],
                       default='max', help='自底向上聚合模式（仅在init-method为bottom_up时有效）')
    parser.add_argument('--extract-representations', action='store_true',
                       help='提取并保存装配体向量表示')

    args = parser.parse_args()

    # 如果只是提取向量表示
    if args.extract_representations:
        logger.info("提取装配体向量表示...")
        representations = extract_assembly_representations(base_dir=args.output_base_dir)

        if representations:
            if args.init_method == 'bottom_up':
                output_path = f"dataset/assembly_representations_{args.init_method}_{args.aggregation_mode}.pkl"
            else:
                output_path = f"dataset/assembly_representations_{args.init_method}.pkl"
            save_assembly_representations(representations, output_path)
        else:
            logger.warning("未找到任何装配体向量表示")
        return

    # 创建转换器
    converter = OptimizedNeo4jToPyGConverter(
        part_features_path=args.part_features_path,
        feature_dim=args.feature_dim,
        learnable_init_std=args.random_init_std,
        init_method=args.init_method,
        aggregation_mode=args.aggregation_mode
    )
    
    try:
        # 获取装配体ID列表
        if args.assembly_ids:
            assembly_ids = args.assembly_ids
            logger.info(f"处理指定的 {len(assembly_ids)} 个装配体")
        else:
            assembly_ids = converter.kg.run_cypher("MATCH (a:Assembly) RETURN a.uuid as assembly_id")
            if isinstance(assembly_ids, list):
                assembly_ids = [record['assembly_id'] for record in assembly_ids]
                logger.info(f"找到 {len(assembly_ids)} 个装配体")
            else:
                logger.error("获取装配体ID失败")
                return
        
        # 限制处理数量
        if args.max_assemblies and len(assembly_ids) > args.max_assemblies:
            assembly_ids = assembly_ids[:args.max_assemblies]
            logger.info(f"限制处理数量为: {args.max_assemblies}")
        
        # 处理装配体
        success_count = 0
        for i, assembly_id in enumerate(assembly_ids, 1):
            logger.info(f"进度: {i}/{len(assembly_ids)}")
            
            if converter.process_assembly(assembly_id, args.output_base_dir):
                success_count += 1
        
        logger.info(f"处理完成: {success_count}/{len(assembly_ids)} 个装配体成功")
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise
    finally:
        converter.close()


if __name__ == "__main__":
    main()
