#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化装配体向量 HDBSCAN 聚类结果，为每个簇生成图片网格。

支持从hdbscan_cluster_assembly_vectors.py生成的聚类结果文件。

用法示例：
python evaluate_scripts/clustering_analysis/visualize_assembly_vector_clusters.py \
    --labels dataset/hdbscan_assembly_vector_labels.pkl \
    --img_dir datasets/fusion360_assembly

# 可视化前10个最大的簇
python evaluate_scripts/clustering_analysis/visualize_assembly_vector_clusters.py \
    --labels dataset/hdbscan_assembly_vector_labels.pkl \
    --img_dir datasets/fusion360_assembly \
    --top_n 10 --samples_per_cluster 16

# 自定义输出目录
python evaluate_scripts/clustering_analysis/visualize_assembly_vector_clusters.py \
    --labels dataset/hdbscan_assembly_vector_labels.pkl \
    --img_dir datasets/fusion360_assembly \
    --out_dir visualization_results/assembly_vector_clusters

可视化选项：
--top_n 10          # 只显示前10个最大的簇
--samples_per_cluster 16  # 每个簇展示16张图片
--min_cluster_size 5      # 只显示大于5个样本的簇
"""
import argparse
import os
import pickle
import random
from collections import defaultdict, Counter
from pathlib import Path

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from tqdm import tqdm
import psycopg2
from psycopg2.extras import RealDictCursor

import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.config import Config


def get_assembly_info_from_db(assembly_ids):
    """从PostgreSQL数据库中获取装配体的详细信息"""
    try:
        # 连接数据库
        conn = psycopg2.connect(**Config.POSTGRES_CONFIG)
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询装配体的详细信息
            placeholders = ','.join(['%s'] * len(assembly_ids))
            query = f"""
                SELECT uuid, name, description, length, width, height, 
                       volume, area, part_count, mass, density
                FROM assemblies 
                WHERE uuid IN ({placeholders})
            """
            cursor.execute(query, assembly_ids)
            results = cursor.fetchall()
            
            # 构建字典映射
            assembly_mapping = {row['uuid']: row for row in results}
            
            print(f"从数据库获取了 {len(assembly_mapping)} 个装配体的详细信息")
            return assembly_mapping
            
    except Exception as e:
        print(f"从数据库获取装配体信息失败: {e}")
        # 返回默认值
        return {aid: {'name': f'assembly_{aid[:8]}', 'description': '未知'} for aid in assembly_ids}
    finally:
        if 'conn' in locals():
            conn.close()


def load_assembly_vector_clustering_results(labels_path):
    """加载装配体向量聚类结果"""
    with open(labels_path, 'rb') as f:
        data = pickle.load(f)
    
    labels = data['labels']
    assembly_ids = data['assembly_ids']
    
    # 检查是否有装配体详细信息，如果没有则从数据库读取
    if 'assembly_info' in data and data['assembly_info']:
        assembly_info = data['assembly_info']
        print("使用已有的装配体详细信息")
    else:
        print("装配体详细信息不存在，从数据库中读取...")
        assembly_info = get_assembly_info_from_db(assembly_ids)
    
    feature_type = data.get('feature_type', 'assembly_vectors')
    params = data.get('params', {})
    metrics = data.get('metrics', {})
    
    # 构建每个簇的样本索引
    clusters = defaultdict(list)
    for i, label in enumerate(labels):
        if label != -1:  # 忽略噪声点
            clusters[int(label)].append(i)
    
    return clusters, labels, assembly_ids, assembly_info, params, feature_type, metrics


def get_cluster_stats(clusters):
    """获取簇的统计信息"""
    stats = []
    for cluster_id, indices in clusters.items():
        stats.append((cluster_id, len(indices)))
    
    # 按簇大小排序
    stats.sort(key=lambda x: x[1], reverse=True)
    return stats


def find_assembly_image_path(assembly_id, img_dir):
    """查找装配体图片路径"""
    # 可能的图片路径
    possible_paths = [
        os.path.join(img_dir, assembly_id, "assembly.png"),        # 主路径
        os.path.join(img_dir, assembly_id, "assembly.jpg"),        # jpg格式
        os.path.join(img_dir, assembly_id, "render.png"),          # 渲染图
        os.path.join(img_dir, assembly_id, "preview.png"),         # 预览图
        os.path.join(img_dir, f"{assembly_id}.png"),               # 直接使用ID
        os.path.join(img_dir, f"{assembly_id}.jpg"),               # 直接使用ID（jpg）
        os.path.join(img_dir, "assemblies", f"{assembly_id}.png"), # 在子目录中
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None


def create_assembly_cluster_grid(cluster_indices, assembly_ids, assembly_info, img_dir, 
                                samples_per_cluster=16, grid_size=None):
    """为一个装配体簇创建图片网格"""
    if grid_size is None:
        # 自动计算网格大小
        grid_size = int(np.ceil(np.sqrt(samples_per_cluster)))
    
    # 如果簇太大，随机采样
    if len(cluster_indices) > samples_per_cluster:
        sample_indices = random.sample(cluster_indices, samples_per_cluster)
    else:
        sample_indices = cluster_indices
    
    # 创建网格
    fig = plt.figure(figsize=(grid_size*2.5, grid_size*2.5))
    
    successful_loads = 0
    
    for i, idx in enumerate(sample_indices):
        if i >= samples_per_cluster:
            break
            
        assembly_id = assembly_ids[idx]
        assembly_detail = assembly_info.get(assembly_id, {})
        assembly_name = assembly_detail.get('name', assembly_id[:8])
        
        # 查找图片路径
        img_path = find_assembly_image_path(assembly_id, img_dir)
        
        ax = fig.add_subplot(grid_size, grid_size, i + 1)
        
        if img_path:
            try:
                img = Image.open(img_path)
                ax.imshow(img)
                ax.axis('off')
                
                # 设置标题，包含更多信息
                title_text = f"{assembly_name}"
                if assembly_detail.get('part_count'):
                    title_text += f"\n({assembly_detail['part_count']}零件)"
                ax.set_title(title_text, fontsize=8)
                successful_loads += 1
                
            except Exception as e:
                print(f"无法加载装配体图片 {img_path}: {e}")
                img_path = None  # 标记为失败
        
        if not img_path:
            # 如果图片不存在，显示装配体信息
            info_text = f"装配体: {assembly_name}\n"
            if assembly_detail.get('part_count'):
                info_text += f"零件数: {assembly_detail['part_count']}\n"
            if assembly_detail.get('volume'):
                info_text += f"体积: {assembly_detail['volume']:.2f}\n"
            if assembly_detail.get('mass'):
                info_text += f"质量: {assembly_detail['mass']:.2f}\n"
            info_text += f"ID: {assembly_id[:8]}..."
            
            ax.text(0.5, 0.5, info_text, 
                   ha='center', va='center', fontsize=6, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.7))
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
    
    plt.tight_layout()
    return fig, successful_loads


def analyze_cluster_characteristics(cluster_indices, assembly_ids, assembly_info):
    """分析簇的特征"""
    cluster_assemblies = [assembly_ids[idx] for idx in cluster_indices]
    cluster_info = [assembly_info.get(aid, {}) for aid in cluster_assemblies]
    
    # 统计零件数量
    part_counts = [info.get('part_count', 0) for info in cluster_info if info.get('part_count')]
    
    # 统计体积
    volumes = [info.get('volume', 0) for info in cluster_info if info.get('volume')]
    
    # 统计质量
    masses = [info.get('mass', 0) for info in cluster_info if info.get('mass')]
    
    stats = {}
    
    if part_counts:
        stats['part_count'] = {
            'min': min(part_counts),
            'max': max(part_counts),
            'mean': np.mean(part_counts),
            'std': np.std(part_counts)
        }
    
    if volumes:
        stats['volume'] = {
            'min': min(volumes),
            'max': max(volumes),
            'mean': np.mean(volumes),
            'std': np.std(volumes)
        }
    
    if masses:
        stats['mass'] = {
            'min': min(masses),
            'max': max(masses),
            'mean': np.mean(masses),
            'std': np.std(masses)
        }
    
    return stats


def create_cluster_summary_report(clusters, labels, assembly_ids, assembly_info, params, metrics):
    """创建聚类摘要报告"""
    total_assemblies = len(assembly_ids)
    noise_count = (labels == -1).sum()
    n_clusters = len(clusters)
    
    report = []
    report.append("# 装配体向量聚类结果摘要")
    report.append("")
    report.append("## 基本信息")
    report.append(f"- 总装配体数量: {total_assemblies}")
    report.append(f"- 簇数: {n_clusters}")
    report.append(f"- 噪声点数量: {noise_count} ({noise_count/total_assemblies*100:.1f}%)")
    report.append("")
    
    report.append("## 聚类参数")
    for key, value in params.items():
        report.append(f"- {key}: {value}")
    report.append("")
    
    report.append("## 评估指标")
    for key, value in metrics.items():
        report.append(f"- {key}: {value}")
    report.append("")
    
    # 簇大小分布
    cluster_stats = get_cluster_stats(clusters)
    report.append("## 簇大小分布")
    report.append("| 簇ID | 大小 | 比例 |")
    report.append("| --- | --- | --- |")
    
    for cluster_id, size in cluster_stats[:10]:  # 显示前10个最大的簇
        percentage = size / total_assemblies * 100
        report.append(f"| {cluster_id} | {size} | {percentage:.1f}% |")
    
    if len(cluster_stats) > 10:
        report.append(f"| ... | ... | ... |")
        report.append(f"总共 {len(cluster_stats)} 个簇")
    
    return "\n".join(report)


def main():
    parser = argparse.ArgumentParser(description="可视化装配体向量聚类结果")
    parser.add_argument("--labels", default="data/clustering/assemblies/hdbscan_assembly_vector_labels.pkl", help="聚类标签文件路径")
    parser.add_argument("--img_dir", default="data/datasets/fusion360_assembly",
                       help="装配体图片目录")
    parser.add_argument("--out_dir", default="results/visualization/assembly_vector_clusters",
                       help="输出目录")
    parser.add_argument("--top_n", type=int, default=10, 
                       help="显示前N个最大的簇")
    parser.add_argument("--samples_per_cluster", type=int, default=16, 
                       help="每个簇显示的样本数量")
    parser.add_argument("--min_cluster_size", type=int, default=1, 
                       help="只显示大于等于此大小的簇")
    parser.add_argument("--save_summary", action='store_true', 
                       help="保存聚类摘要报告")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.out_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载聚类结果
    print("加载聚类结果...")
    clusters, labels, assembly_ids, assembly_info, params, feature_type, metrics = load_assembly_vector_clustering_results(args.labels)
    
    # 获取簇统计信息
    cluster_stats = get_cluster_stats(clusters)
    
    print(f"总共 {len(clusters)} 个簇")
    print(f"最大簇大小: {cluster_stats[0][1] if cluster_stats else 0}")
    
    # 过滤簇
    filtered_clusters = [(cid, size) for cid, size in cluster_stats 
                        if size >= args.min_cluster_size]
    
    if args.top_n > 0:
        filtered_clusters = filtered_clusters[:args.top_n]
    
    print(f"将可视化 {len(filtered_clusters)} 个簇")
    
    # 生成每个簇的可视化
    for i, (cluster_id, cluster_size) in enumerate(tqdm(filtered_clusters, desc="生成簇可视化")):
        cluster_indices = clusters[cluster_id]
        
        print(f"\n处理簇 {cluster_id} (大小: {cluster_size})")
        
        # 创建图片网格
        fig, successful_loads = create_assembly_cluster_grid(
            cluster_indices, assembly_ids, assembly_info, args.img_dir,
            samples_per_cluster=args.samples_per_cluster
        )
        
        # 分析簇特征
        cluster_characteristics = analyze_cluster_characteristics(
            cluster_indices, assembly_ids, assembly_info
        )
        
        # 设置图片标题
        title = f"簇 {cluster_id} - {cluster_size} 个装配体"
        if successful_loads < len(cluster_indices[:args.samples_per_cluster]):
            title += f" (显示 {successful_loads}/{min(len(cluster_indices), args.samples_per_cluster)} 张图片)"
        
        # 添加特征信息到标题
        if 'part_count' in cluster_characteristics:
            pc_stats = cluster_characteristics['part_count']
            title += f"\n平均零件数: {pc_stats['mean']:.1f} (±{pc_stats['std']:.1f})"
        
        fig.suptitle(title, fontsize=14, y=0.98)
        
        # 保存图片
        output_file = output_dir / f"cluster_{cluster_id:03d}_size_{cluster_size}.png"
        fig.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        print(f"  已保存到: {output_file}")
        print(f"  成功加载图片: {successful_loads}/{min(len(cluster_indices), args.samples_per_cluster)}")
        
        if cluster_characteristics:
            print(f"  簇特征:")
            for char_type, stats in cluster_characteristics.items():
                print(f"    {char_type}: 平均={stats['mean']:.2f}, 标准差={stats['std']:.2f}")
    
    # 生成摘要报告
    if args.save_summary:
        summary_report = create_cluster_summary_report(
            clusters, labels, assembly_ids, assembly_info, params, metrics
        )
        
        summary_file = output_dir / "clustering_summary.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        print(f"\n摘要报告已保存到: {summary_file}")
    
    # 生成总览图
    print("\n生成聚类总览...")
    
    # 创建一个显示所有簇大小的条形图
    plt.figure(figsize=(12, 6))
    cluster_ids, cluster_sizes = zip(*cluster_stats[:20])  # 显示前20个簇
    
    plt.bar(range(len(cluster_ids)), cluster_sizes)
    plt.xlabel('簇ID')
    plt.ylabel('簇大小')
    plt.title(f'装配体向量聚类结果 - 前{len(cluster_ids)}个最大的簇')
    plt.xticks(range(len(cluster_ids)), cluster_ids, rotation=45)
    
    # 添加数值标签
    for i, size in enumerate(cluster_sizes):
        plt.text(i, size + max(cluster_sizes) * 0.01, str(size), 
                ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    overview_file = output_dir / "cluster_size_overview.png"
    plt.savefig(overview_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"总览图已保存到: {overview_file}")
    print(f"\n可视化完成！结果保存在: {output_dir}")


if __name__ == "__main__":
    main()
