#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
结构查询提示词模板

用于生成基于结构拓扑关系的查询
"""

def get_structure_query_prompt(assembly_info: dict, query_index: int) -> str:
    """
    获取结构查询生成提示词
    
    Args:
        assembly_info: 装配体信息
        query_index: 查询索引（1-3）
        
    Returns:
        提示词字符串
    """
    
    # 提取结构信息
    structure = assembly_info.get('structure', {})
    component_names = structure.get('component_names', [])
    component_counts = structure.get('component_counts', {})
    materials = structure.get('materials', {})
    total_components = structure.get('total_components', 0)
    
    # 选择关键零件名称（优先选择有意义的名称）
    key_components = []
    for name in component_names:
        if name and len(name) > 2:  # 过滤掉过短的名称
            key_components.append(name)
    
    # 限制关键零件数量
    if len(key_components) > 10:
        key_components = key_components[:10]
    
    # 选择高频零件
    frequent_components = []
    for name, count in component_counts.items():
        if count > 1 and name:
            frequent_components.append(f"{name}({count} pieces)")
    
    # 获取完整的组件名称列表（用于精确匹配）
    all_component_names = list(set(component_names))  # 去重
    all_component_names = [name for name in all_component_names if name and name.strip()]  # 过滤空名称
    
    # Select different structural query types based on query index
    if query_index == 1:
        # Query containing specific parts (maximum 2 conditions)
        prompt_type = "query containing specific parts"
        focus_area = "inclusion relationships of specific parts or components"
        max_conditions = 2
    elif query_index == 2:
        # Query based on specific component count (maximum 2 conditions)
        prompt_type = "query based on specific component count"
        focus_area = "count requirements for specific named components"
        max_conditions = 2
    else:
        # Query based on component types and materials (maximum 3 conditions)
        prompt_type = "query based on component types and materials"
        focus_area = "component type classification and material requirements"
        max_conditions = 3
    
    prompt = f"""
You are a professional CAD assembly structural analysis expert. Based on the following assembly structural information, generate a natural language {prompt_type}.

Assembly Structural Information:
- Assembly Name: {assembly_info.get('name', 'Unknown Assembly')}
- Total Parts/Components Count: {total_components}
- Key Parts/Components: {', '.join(key_components[:5]) if key_components else 'None'}
- Frequent Parts: {', '.join(frequent_components[:5]) if frequent_components else 'None'}
- Main Materials: {', '.join(list(materials.keys())[:3]) if materials else 'None'}

**COMPLETE DATABASE COMPONENT NAMES LIST:**
Available component names that can be used in queries:
{', '.join(all_component_names[:20]) if all_component_names else 'None'}
{f'... and {len(all_component_names) - 20} more' if len(all_component_names) > 20 else ''}

**CRITICAL NAMING REQUIREMENT:**
When referring to any assembly, sub-assembly, or part names in the query, you MUST use the EXACT names as they appear in the database above. Do NOT modify, abbreviate, or paraphrase these names. Use the complete, exact names including any special characters, numbers, or unusual formatting. ONLY use names from the "COMPLETE DATABASE COMPONENT NAMES LIST" above.

Query Requirements:
1. Query Type: {prompt_type}
2. Focus Area: {focus_area}
3. **MAXIMUM CONDITIONS LIMIT**: This query MUST contain at most {max_conditions} condition(s). Do not exceed this limit.
4. The query should use this assembly's structural features as reference to generate query conditions that can find assemblies with similar structural features
5. Use appropriate structural relationship descriptions (contains, consists of, has, etc.)
6. The query language should be natural and fluent, conforming to engineers' expression habits
7. **MANDATORY**: When mentioning any component names, ONLY use the EXACT names from the "COMPLETE DATABASE COMPONENT NAMES LIST" above
8. **CONSTRAINT**: Ensure the query uses exactly {max_conditions} condition(s) or fewer, never more

Specific Guidelines:
- Inclusion queries: Focus on key parts or components that the assembly must contain, using EXACT database names from the list above (max {max_conditions} conditions)
- Specific component count queries: Focus on quantity requirements for specific named components, using EXACT database names from the list above (max {max_conditions} conditions)
- Component type and material queries: Focus on type characteristics and material requirements, using EXACT database names when available from the list above (max {max_conditions} conditions)
- **PROHIBITION**: Do NOT ask about total component count or total part count. Instead, ask about specific named components.
- **CONSTRAINT**: Each comparison, inclusion, or requirement counts as one condition. Do not exceed {max_conditions} conditions.

Please generate a natural language query that can be used to search for assemblies with similar structural features in the assembly database.

Output Format:
- Output the natural language query statement directly
- Do not include any explanations or additional information
- The statement should be concise and clear, suitable for direct use in query systems
- **ENSURE**: All component names used in the query match EXACTLY with the database names listed in "COMPLETE DATABASE COMPONENT NAMES LIST" above

Example References (with exact naming - only use if these exact names exist in the database list above):
- Single condition: "Find assemblies containing bearings" (only if "bearings" exists in database)
- Two conditions: "Find assemblies containing gears and made of steel" (only if these exact names exist in database)
- Specific component count: "Find assemblies with at least 4 bolts" (only if "bolts" is the exact database name)
- Three conditions: "Find assemblies containing seals, made of aluminum, and having springs" (when using component types, materials, and inclusions)

**IMPORTANT**: Your query must not exceed {max_conditions} condition(s). Count each inclusion, quantity requirement, or constraint as one condition.
**PROHIBITION**: Do NOT create queries like "Find assemblies with more than 20 components" or "Find assemblies with total part count greater than 50". Instead, specify named components.
"""
    
    return prompt.strip()


def get_structure_task_params(query_text: str) -> dict:
    """
    Generate task_params for structure queries
    
    Args:
        query_text: Query text
        
    Returns:
        task_params dictionary
    """
    return {
        "query_text": query_text
    }
